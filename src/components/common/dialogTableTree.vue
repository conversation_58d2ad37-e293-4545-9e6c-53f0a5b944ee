<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="false"
      :filter="true"
      :checkAll="false"
      :checkStrictly="true"
      :filter-source-data="filterSourceData"
      @checkbox-change="onCheckboxChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, useTemplateRef, nextTick, watch } from 'vue'
  import { TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'

  const emit = defineEmits(['checkbox-change'])

  const props = withDefaults(
    defineProps<{
      showOnlyOrg?: boolean
      defaultCheckKeys?: string[] | string
    }>(),
    {
      showOnlyOrg: false,
      defaultCheckKeys: () => [],
    }
  )

  const tableTreeRef = useTemplateRef('tableTree')

  const onCheckboxChange: VxeTableEvents.CellDblclick<TreeNodeData> = row => {
    emit('checkbox-change', row)
  }
  const filterSourceData = (row: TreeNodeData) => {
    if (!props.showOnlyOrg) {
      return true
    }
    if (row.nodeType !== TreeNodeType.Org) {
      row.parentOrgId = ''
      return false
    }
    return true
  }

  // 标准化 defaultCheckKeys 为数组格式
  const normalizeCheckKeys = (keys: string[] | string): string[] => {
    if (typeof keys === 'string') {
      return keys ? [keys] : []
    }
    return Array.isArray(keys) ? keys : []
  }

  // 设置节点勾选状态
  const setNodeCheckStatus = async (checkKeys: string[]) => {
    await nextTick()

    if (!tableTreeRef.value?.setCheckboxRowByRid) {
      return
    }

    // 首先清除所有节点的勾选状态
    if (tableTreeRef.value?.clearAllCheckboxRow) {
      tableTreeRef.value.clearAllCheckboxRow()
    }

    // 然后设置指定节点为勾选状态
    checkKeys.forEach(dmrId => {
      const orgData = bfglob.gorgData.getDataByIndex(dmrId)
      if (orgData && orgData.rid) {
        tableTreeRef.value.setCheckboxRowByRid(orgData.rid, true)
      }
    })
  }

  const setDefaultCheckedNodes = async () => {
    const checkKeys = normalizeCheckKeys(props.defaultCheckKeys)
    if (checkKeys.length === 0) {
      return
    }
    await setNodeCheckStatus(checkKeys)
  }

  // 监听 defaultCheckKeys 变化
  watch(
    () => props.defaultCheckKeys,
    newKeys => {
      const checkKeys = normalizeCheckKeys(newKeys)
      setNodeCheckStatus(checkKeys)
    },
    { deep: true }
  )

  onMounted(() => {
    setTimeout(() => {
      setDefaultCheckedNodes()
    }, 500) // 增加延迟时间确保树完全加载
  })
</script>

<style lang="scss">
  .dialog-tree-wrapper {
    height: 100%;
    width: 100%;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
